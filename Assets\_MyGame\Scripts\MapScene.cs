using UnityEngine;
using UnityEngine.Tilemaps;
using System;
using System.Collections;
using FairyGUI;

public class MapScene : MonoBehaviour
{
    public GameObject mapGo;
    public GameObject flyTarget;

    public static event Action<Tilemap, Vector3Int, TileBase> OnTileClickedEvent;

    [Header("拖动设置")]
    public float dragSpeed = 1f;
    public float dragThreshold = 10f; // 拖拽阈值，像素单位

    [Header("缩放设置")]
    public float zoomSpeed = 5f;
    public float minZoom = 10f;
    private float maxZoom;

    [Header("地图边界设置")]
    public int mapWidth = 4096;
    public int mapHeight = 4096;
    public Vector2 mapCenter = Vector2.zero;

    [Header("UI镂空区域设置")]
    [Tooltip("是否使用UI镂空区域作为相机边界")]
    public bool useUIBounds = true;
    [Tooltip("UI设计分辨率宽度（如果FairyGUI未设置则使用此值）")]
    public int uiDesignWidth = 1080;
    [Tooltip("UI设计分辨率高度（如果FairyGUI未设置则使用此值）")]
    public int uiDesignHeight = 1920;
    [Tooltip("UI上部分高度（像素）")]
    public int uiTopHeight = 300;
    [Tooltip("UI下部分高度（像素）")]
    public int uiBottomHeight = 500;

    [Header("调试信息")]
    [SerializeField, Tooltip("当前镂空区域世界坐标尺寸")]
    private Vector2 currentHollowBounds;

    [Header("飞行动画设置")]
    public float flyDuration = 1f;
    public AnimationCurve flyCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
    public bool removeTileOnClick = true;
    public AudioClip tileClickSound;

    private Camera mainCamera;
    private bool isDragging = false;
    private bool isPotentialDrag = false; // 是否可能开始拖拽
    private Vector3 lastMousePosition;
    private Vector3 dragStartPosition; // 拖拽开始位置

    // 触摸缩放相关变量
    private bool isTouchZooming = false;
    private float lastTouchDistance = 0f;

    void Start()
    {
        InitializeComponents();
    }

    void Update()
    {
        HandleInput();
    }

    void InitializeComponents()
    {
        mainCamera = Camera.main;
        if (mainCamera == null)
            mainCamera = FindObjectOfType<Camera>();

        maxZoom = mainCamera.orthographicSize;
    }

    void HandleInput()
    {
        HandleZoom();
        HandleTouchInput();

        if (Input.GetMouseButtonDown(0))
        {
            if (!isTouchZooming)
            {
                StartPotentialDrag();
            }
        }
        else if (Input.GetMouseButton(0))
        {
            if (isPotentialDrag && !isDragging)
            {
                CheckDragThreshold();
            }
            else if (isDragging)
            {
                UpdateDrag();
            }
        }
        else if (Input.GetMouseButtonUp(0))
        {
            HandleClickAndEndDrag();
        }
    }

    void StartPotentialDrag()
    {
        isPotentialDrag = true;
        isDragging = false;
        dragStartPosition = Input.mousePosition;
        lastMousePosition = Input.mousePosition;
    }

    void CheckDragThreshold()
    {
        Vector3 currentMousePosition = Input.mousePosition;
        float dragDistance = Vector3.Distance(dragStartPosition, currentMousePosition);

        if (dragDistance >= dragThreshold)
        {
            isDragging = true;
            isPotentialDrag = false;
            lastMousePosition = currentMousePosition;
        }
    }

    void UpdateDrag()
    {
        Vector3 currentMousePosition = Input.mousePosition;
        Vector3 mouseDelta = lastMousePosition - currentMousePosition;

        // 将屏幕坐标差值转换为世界坐标差值
        Vector3 worldDelta = mainCamera.ScreenToWorldPoint(mouseDelta) - mainCamera.ScreenToWorldPoint(Vector3.zero);

        // 只在X和Y轴移动，保持Z坐标不变
        Vector3 newPosition = mainCamera.transform.position + new Vector3(worldDelta.x * dragSpeed, worldDelta.y * dragSpeed, 0);

        newPosition = ClampCameraPosition(newPosition);

        mainCamera.transform.position = newPosition;
        lastMousePosition = currentMousePosition;
    }

    void HandleClickAndEndDrag()
    {
        // 只有在没有发生拖拽的情况下才处理点击
        if (!isDragging && (isPotentialDrag || !isTouchZooming))
        {
            HandleClick();
        }

        EndDrag();
    }

    void EndDrag()
    {
        isDragging = false;
        isPotentialDrag = false;
    }

    void HandleZoom()
    {
        float scroll = Input.GetAxis("Mouse ScrollWheel");
        if (scroll != 0f)
        {
            float currentSize = mainCamera.orthographicSize;
            float newSize = currentSize - scroll * zoomSpeed;
            newSize = Mathf.Clamp(newSize, minZoom, maxZoom);

            mainCamera.orthographicSize = newSize;

            // 缩放后重新限制相机位置
            Vector3 clampedPosition = ClampCameraPosition(mainCamera.transform.position);
            mainCamera.transform.position = clampedPosition;
        }
    }

    void HandleTouchInput()
    {
        if (Input.touchCount == 2)
        {
            Touch touch1 = Input.GetTouch(0);
            Touch touch2 = Input.GetTouch(1);

            float currentTouchDistance = Vector2.Distance(touch1.position, touch2.position);

            if (!isTouchZooming)
            {
                isTouchZooming = true;
                lastTouchDistance = currentTouchDistance;
                isDragging = false;
                isPotentialDrag = false;
            }
            else
            {
                float deltaDistance = currentTouchDistance - lastTouchDistance;
                float zoomFactor = deltaDistance * 0.01f; // 调整缩放敏感度

                float currentSize = mainCamera.orthographicSize;
                float newSize = currentSize - zoomFactor * zoomSpeed;
                newSize = Mathf.Clamp(newSize, minZoom, maxZoom);

                mainCamera.orthographicSize = newSize;

                // 缩放后重新限制相机位置
                Vector3 clampedPosition = ClampCameraPosition(mainCamera.transform.position);
                mainCamera.transform.position = clampedPosition;

                lastTouchDistance = currentTouchDistance;
            }
        }
        else
        {
            isTouchZooming = false;
        }
    }

    void HandleClick()
    {
        Vector3 mouseWorldPos = mainCamera.ScreenToWorldPoint(Input.mousePosition);
        mouseWorldPos.z = 0f;
        RaycastHit2D hit = Physics2D.Raycast(mouseWorldPos, Vector2.zero);
        if (hit.collider != null)
        {
            if (hit.collider.TryGetComponent(out PolygonCollider2D collider))
            {
                if (collider != null)
                {
                    OnTileClicked(collider.gameObject);
                    Destroy(collider.gameObject);
                }
            }
        }
    }

    void OnTileClicked(GameObject tileGo)
    {
        if (flyTarget != null)
        {
            StartFlyAnimation(tileGo);
        }
    }

    void StartFlyAnimation(GameObject tilemapObject)
    {
        // 创建一个临时的飞行对象来表示被点击的瓦片
        GameObject flyObject = new GameObject("FlyingTile");

        // 添加SpriteRenderer来显示瓦片
        SpriteRenderer spriteRenderer = flyObject.AddComponent<SpriteRenderer>();

        // 获取瓦片的精灵
        var srcRenderer = tilemapObject.GetComponent<SpriteRenderer>();
        spriteRenderer.sprite = srcRenderer.sprite;

        var startPos = tilemapObject.transform.position;
        flyObject.transform.position = startPos;

        // 开始飞行协程
        StartCoroutine(FlyToTarget(flyObject, spriteRenderer, startPos, flyTarget.transform.position));
    }

    IEnumerator FlyToTarget(GameObject flyObject, SpriteRenderer spriteRenderer, Vector3 startPos, Vector3 targetPos)
    {
        float elapsedTime = 0f;
        Vector3 initialScale = flyObject.transform.localScale;

        while (elapsedTime < flyDuration)
        {
            elapsedTime += Time.deltaTime;
            float progress = elapsedTime / flyDuration;
            float curveValue = flyCurve.Evaluate(progress);

            // 线性插值位置
            Vector3 currentPos = Vector3.Lerp(startPos, targetPos, curveValue);

            // 添加抛物线效果
            float height = Mathf.Sin(progress * Mathf.PI) * 2f;
            currentPos.y += height;

            flyObject.transform.position = currentPos;

            // 添加旋转效果
            flyObject.transform.Rotate(0, 0, 360f * Time.deltaTime);

            // 添加缩放效果，飞行过程中逐渐变小
            float scale = Mathf.Lerp(1f, 0.3f, progress);
            flyObject.transform.localScale = initialScale * scale;

            // 添加透明度变化
            if (spriteRenderer != null)
            {
                Color color = spriteRenderer.color;
                color.a = Mathf.Lerp(1f, 0.5f, progress);
                spriteRenderer.color = color;
            }

            yield return null;
        }

        // 动画结束后销毁飞行对象
        Destroy(flyObject);
    }

    Vector3 ClampCameraPosition(Vector3 targetPosition)
    {
        if (mainCamera == null) return targetPosition;

        float cameraHeight, cameraWidth;

        if (useUIBounds)
        {
            // 使用UI镂空区域计算相机视野边界
            var uiBounds = CalculateUIHollowBounds();
            cameraHeight = uiBounds.y;
            cameraWidth = uiBounds.x;
        }
        else
        {
            // 使用传统的相机视野计算
            cameraHeight = mainCamera.orthographicSize * 2f;
            cameraWidth = cameraHeight * mainCamera.aspect;
        }

        // 使用手动设置的地图边界
        float minX = mapCenter.x - mapWidth * 0.005f + cameraWidth * 0.5f;
        float maxX = mapCenter.x + mapWidth * 0.005f - cameraWidth * 0.5f;
        float minY = mapCenter.y - mapHeight * 0.005f + cameraHeight * 0.5f;
        float maxY = mapCenter.y + mapHeight * 0.005f - cameraHeight * 0.5f;

        // 如果地图比相机视野小，固定在中心
        if (maxX < minX)
        {
            minX = maxX = mapCenter.x;
        }

        if (maxY < minY)
        {
            minY = maxY = mapCenter.y;
        }

        targetPosition.x = Mathf.Clamp(targetPosition.x, minX, maxX);
        targetPosition.y = Mathf.Clamp(targetPosition.y, minY, maxY);

        return targetPosition;
    }

    /// <summary>
    /// 获取FairyGUI的设计分辨率
    /// </summary>
    /// <returns>设计分辨率</returns>
    Vector2 GetUIDesignResolution()
    {
        var scaler = Stage.inst?.gameObject.GetComponent<UIContentScaler>();
        if (scaler != null && scaler.scaleMode == UIContentScaler.ScaleMode.ScaleWithScreenSize)
        {
            return new Vector2(scaler.designResolutionX, scaler.designResolutionY);
        }
        return new Vector2(uiDesignWidth, uiDesignHeight);
    }

    /// <summary>
    /// 计算UI镂空区域在世界坐标中的边界
    /// </summary>
    /// <returns>镂空区域的宽度和高度</returns>
    Vector2 CalculateUIHollowBounds()
    {
        // 获取当前屏幕尺寸
        float screenWidth = Screen.width;
        float screenHeight = Screen.height;

        // 获取UI设计分辨率
        var designResolution = GetUIDesignResolution();
        float designWidth = designResolution.x;
        float designHeight = designResolution.y;

        // 计算UI缩放因子
        float scaleX = screenWidth / designWidth;
        float scaleY = screenHeight / designHeight;
        float scale = Mathf.Min(scaleX, scaleY); // 使用较小的缩放因子保持比例

        // 计算实际UI尺寸
        float actualUIWidth = designWidth * scale;
        float actualUIHeight = designHeight * scale;

        // 计算实际的UI上下部分高度
        float actualTopHeight = uiTopHeight * scale;
        float actualBottomHeight = uiBottomHeight * scale;

        // 计算镂空区域的屏幕像素尺寸
        float hollowScreenWidth = actualUIWidth;
        float hollowScreenHeight = actualUIHeight - actualTopHeight - actualBottomHeight;

        // 将屏幕像素转换为世界坐标
        // 使用相机的正交大小和屏幕比例来计算
        float worldHeight = mainCamera.orthographicSize * 2f;
        float worldWidth = worldHeight * mainCamera.aspect;

        // 计算镂空区域在世界坐标中的尺寸
        float hollowWorldWidth = hollowScreenWidth / screenWidth * worldWidth;
        float hollowWorldHeight = hollowScreenHeight / screenHeight * worldHeight;

        var result = new Vector2(hollowWorldWidth, hollowWorldHeight);

        // 更新调试信息
        currentHollowBounds = result;

        return result;
    }

#if UNITY_EDITOR
    void OnValidate()
    {
        // 在编辑器中实时更新调试信息
        if (Application.isPlaying && mainCamera != null && useUIBounds)
        {
            currentHollowBounds = CalculateUIHollowBounds();
        }
    }
#endif
}