# UI镂空区域相机边界功能说明

## 功能概述

此功能允许MapScene中的相机视野以FairyGUI的镂空区域作为缩放拖动的边界，而不是使用传统的相机视野边界。

## 使用场景

当你的游戏UI布局如下时：
- FairyGUI设计分辨率：1080x1920
- UI上部分（如状态栏、菜单等）：300像素
- UI下部分（如操作按钮、信息栏等）：500像素
- 中间镂空区域：1080x1120像素

镂空区域就是游戏场景的可视区域，相机应该以这个区域作为边界进行限制。

## 配置步骤

### 1. 在MapScene组件中设置

在MapScene组件的Inspector面板中：

1. **启用UI边界模式**：
   - 勾选 `Use UI Bounds`

2. **设置UI布局参数**：
   - `UI Design Width`: UI设计分辨率宽度（默认1080）
   - `UI Design Height`: UI设计分辨率高度（默认1920）
   - `UI Top Height`: UI上部分高度（像素，如300）
   - `UI Bottom Height`: UI下部分高度（像素，如500）

### 2. 自动获取FairyGUI设置

如果你的项目中已经配置了FairyGUI的UIContentScaler，系统会自动获取设计分辨率，无需手动设置。

### 3. 使用示例脚本

可以使用提供的`UIBoundsExample`脚本来快速设置：

```csharp
// 获取MapScene组件
var mapScene = FindObjectOfType<MapScene>();

// 启用UI边界模式
mapScene.useUIBounds = true;

// 设置UI布局
mapScene.uiTopHeight = 300;    // 顶部UI高度
mapScene.uiBottomHeight = 500; // 底部UI高度
```

## 工作原理

1. **获取屏幕信息**：获取当前屏幕分辨率
2. **计算UI缩放**：根据设计分辨率和屏幕分辨率计算缩放比例
3. **计算镂空区域**：
   - 实际UI尺寸 = 设计尺寸 × 缩放比例
   - 镂空区域高度 = UI总高度 - 顶部高度 - 底部高度
4. **转换世界坐标**：将屏幕像素尺寸转换为世界坐标尺寸
5. **应用相机边界**：使用镂空区域尺寸限制相机移动范围

## 调试功能

### Inspector调试信息
- `Current Hollow Bounds`: 显示当前镂空区域的世界坐标尺寸

### 示例脚本调试方法
- `切换UI边界模式`: 快速开关UI边界功能
- `打印UI信息`: 输出详细的UI计算信息到控制台

## 注意事项

1. **FairyGUI依赖**：此功能需要FairyGUI框架支持
2. **设计分辨率**：确保设置的设计分辨率与FairyGUI配置一致
3. **UI布局**：UI上下部分高度应该与实际UI设计匹配
4. **性能考虑**：镂空区域计算会在每次相机移动时执行，但计算量很小

## 兼容性

- 支持不同屏幕分辨率和比例
- 自动适配横屏/竖屏切换
- 兼容FairyGUI的各种缩放模式

## 故障排除

### 相机边界不正确
1. 检查UI设计分辨率设置是否正确
2. 确认UI上下部分高度设置是否匹配实际UI
3. 查看调试信息中的计算结果

### 无法获取FairyGUI设置
1. 确保FairyGUI已正确初始化
2. 检查UIContentScaler组件是否存在
3. 手动设置设计分辨率参数

### 镂空区域计算错误
1. 检查屏幕分辨率获取是否正确
2. 确认缩放比例计算逻辑
3. 使用调试功能查看详细计算过程
