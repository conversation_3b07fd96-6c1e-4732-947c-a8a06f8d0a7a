using UnityEngine;
using FairyGUI;

/// <summary>
/// 示例脚本：展示如何设置UI镂空区域作为相机边界
/// </summary>
public class UIBoundsExample : MonoBehaviour
{
    [Header("示例设置")]
    [Tooltip("MapScene组件引用")]
    public MapScene mapScene;
    
    [<PERSON><PERSON>("UI布局示例")]
    [Toolt<PERSON>("顶部UI高度（像素）")]
    public int topUIHeight = 300;
    [<PERSON>lt<PERSON>("底部UI高度（像素）")]
    public int bottomUIHeight = 500;
    
    void Start()
    {
        // 如果没有手动指定MapScene，尝试自动查找
        if (mapScene == null)
        {
            mapScene = FindObjectOfType<MapScene>();
        }
        
        if (mapScene != null)
        {
            SetupUIBounds();
        }
        else
        {
            Debug.LogWarning("未找到MapScene组件！");
        }
    }
    
    /// <summary>
    /// 设置UI镂空区域
    /// </summary>
    void SetupUIBounds()
    {
        // 启用UI边界模式
        mapScene.useUIBounds = true;
        
        // 设置UI上下部分的高度
        mapScene.uiTopHeight = topUIHeight;
        mapScene.uiBottomHeight = bottomUIHeight;
        
        // 尝试从FairyGUI获取设计分辨率
        var scaler = Stage.inst?.gameObject.GetComponent<UIContentScaler>();
        if (scaler != null && scaler.scaleMode == UIContentScaler.ScaleMode.ScaleWithScreenSize)
        {
            mapScene.uiDesignWidth = scaler.designResolutionX;
            mapScene.uiDesignHeight = scaler.designResolutionY;
            Debug.Log($"从FairyGUI获取设计分辨率: {scaler.designResolutionX}x{scaler.designResolutionY}");
        }
        else
        {
            Debug.Log($"使用默认设计分辨率: {mapScene.uiDesignWidth}x{mapScene.uiDesignHeight}");
        }
        
        Debug.Log($"UI镂空区域设置完成 - 顶部:{topUIHeight}px, 底部:{bottomUIHeight}px");
    }
    
    /// <summary>
    /// 切换UI边界模式
    /// </summary>
    [ContextMenu("切换UI边界模式")]
    public void ToggleUIBounds()
    {
        if (mapScene != null)
        {
            mapScene.useUIBounds = !mapScene.useUIBounds;
            Debug.Log($"UI边界模式: {(mapScene.useUIBounds ? "启用" : "禁用")}");
        }
    }
    
    /// <summary>
    /// 打印当前UI信息
    /// </summary>
    [ContextMenu("打印UI信息")]
    public void PrintUIInfo()
    {
        if (mapScene == null) return;
        
        Debug.Log("=== UI镂空区域信息 ===");
        Debug.Log($"使用UI边界: {mapScene.useUIBounds}");
        Debug.Log($"设计分辨率: {mapScene.uiDesignWidth}x{mapScene.uiDesignHeight}");
        Debug.Log($"顶部UI高度: {mapScene.uiTopHeight}px");
        Debug.Log($"底部UI高度: {mapScene.uiBottomHeight}px");
        Debug.Log($"屏幕分辨率: {Screen.width}x{Screen.height}");
        
        // 计算镂空区域
        float screenWidth = Screen.width;
        float screenHeight = Screen.height;
        float scaleX = screenWidth / mapScene.uiDesignWidth;
        float scaleY = screenHeight / mapScene.uiDesignHeight;
        float scale = Mathf.Min(scaleX, scaleY);
        
        float actualUIWidth = mapScene.uiDesignWidth * scale;
        float actualUIHeight = mapScene.uiDesignHeight * scale;
        float actualTopHeight = mapScene.uiTopHeight * scale;
        float actualBottomHeight = mapScene.uiBottomHeight * scale;
        float hollowHeight = actualUIHeight - actualTopHeight - actualBottomHeight;
        
        Debug.Log($"UI缩放比例: {scale:F2}");
        Debug.Log($"实际UI尺寸: {actualUIWidth:F0}x{actualUIHeight:F0}");
        Debug.Log($"镂空区域尺寸: {actualUIWidth:F0}x{hollowHeight:F0}");
    }
}
