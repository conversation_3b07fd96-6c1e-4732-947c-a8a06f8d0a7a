using System;
using UnityEngine.SceneManagement;

public class BattleHandler : HandlerBase
{
    private static readonly string BattleSceneUrl = "Scenes/Battle";
    enum Step
    {
        Empty,
        LoadScene,
        GameStart,
        GameOver,
        GameVictory,
        GameLoop
    }

    private Step step;
    public override void Enter()
    {
        step = Step.LoadScene;
    }

    public override void Update()
    {
        switch (step)
        {
            case Step.GameLoop:
                GameLoop();
                break;
            case Step.Empty:
                break;
            case Step.LoadScene:
                LoadScene();
                break;
            case Step.GameStart:
                GameStart();
                break;
            case Step.GameOver:
                GameOver();
                break;
            case Step.GameVictory:
                GameVictory();
                break;

        }
    }

    private void LoadScene()
    {
        step = Step.Empty;
        var isSceneCompleted = false;
        var isBattleUICompleted = false;
        AssetBundleManager.LoadScene(BattleSceneUrl, LoadSceneMode.Additive, (scene) =>
        {
            isSceneCompleted = true;

            if (isBattleUICompleted)
            {
                step = Step.GameStart;
            }
        });
        FUILoader.LoadPackage("Battle", doneAction: () =>
        {
            isBattleUICompleted = true;
            if (isSceneCompleted)
            {
                step = Step.GameStart;
            }
        });
    }

    private void GameStart()
    {
        Panel.Create<BattlePanel>(panel =>
        {
            // step = Step.GameLoop;
        });
    }

    private void GameLoop()
    {
        throw new NotImplementedException();
    }

    private void GameVictory()
    {
        throw new NotImplementedException();
    }

    private void GameOver()
    {
        throw new NotImplementedException();
    }
}